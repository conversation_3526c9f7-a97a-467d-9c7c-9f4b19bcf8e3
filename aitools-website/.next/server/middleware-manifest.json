{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_915e9f02._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_4ac6bddb._.js", "server/edge/chunks/[root-of-the-server]__e8693e40._.js", "server/edge/chunks/edge-wrapper_ff61254e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|robots.txt|sitemap.xml|uploads).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "z32E33zlP9nvGtJvjxN4efsCMH50n6HIiGeqjezkGUw=", "__NEXT_PREVIEW_MODE_ID": "daabd70ca2014d0abb0d95f395563972", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8219286fc64defa774fbdb36096911d85e462d7898a074f0a4ecafef18bd6170", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b73b026793cbce2bd7dd18aa9e46e1540149ea5240f0164d3aab335f5d6065f1"}}}, "sortedMiddleware": ["/"], "functions": {}}