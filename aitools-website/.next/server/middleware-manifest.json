{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_915e9f02._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_4ac6bddb._.js", "server/edge/chunks/[root-of-the-server]__e8693e40._.js", "server/edge/chunks/edge-wrapper_ff61254e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|robots.txt|sitemap.xml|uploads).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "UA5FhFitQYGmT0zXOaYe3T6CKsj+0avzuZE0JYkEkAs=", "__NEXT_PREVIEW_MODE_ID": "51c1d9a2cb38ff619dc2ade61bed647a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6dffe71911e86329cf31174e9cc6ba86ad7ef32ada2873ee9ebf8e704f53b59f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bd3fd918efdbbe1c5396e17176ddbf5656822258d699c49aeb721efc95fd4ff0"}}}, "sortedMiddleware": ["/"], "functions": {}}